<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <link rel="icon" type="image/svg+xml" href="/vite.svg" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <link rel="stylesheet" href="/src/ui-test/style.css" />
        <script type="module" defer src="/src/ui-test/main.ts"></script>
        <title>CaroJS</title>
    </head>
    <body>
        <main>
            <h1>CaroJS</h1>
            <pre id="board"></pre>
            <div>
                <input type="text" id="input" />
                <button id="play">Play</button>
                <button id="reset">Reset</button>
            </div>
        </main>
    </body>
</html>
