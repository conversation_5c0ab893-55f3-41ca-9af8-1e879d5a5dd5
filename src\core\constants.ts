export enum GAME_MODE {
    /** Allow overlines. */
    STANDARD,

    /**
     * - <PERSON><PERSON><PERSON><PERSON> 1st move must be in the center.
     * - WHITE 1st move could be anywhere.
     * - BLACK 2nd move must be at least 2 squares/empty intersection away from the center.
     * - No overlines.
     */
    PRO,

    /**
     * - <PERSON><PERSON><PERSON>K 1st move must be in the center,
     * - WHITE 1st move could be anywhere.
     * - BLACK 2nd move must be at least 3 squares/empty intersection away from the center.
     * - No overlines.
     */
    LONG_PRO,

    /**
     * - 1st player places 2 BLACKs and 1 WHITE anywhere.
     * - 2nd player chooses to be BLACK or WHITE:
     *   - If 2nd player chooses WHITE, they continue playing the 2nd WHITE.
     *   - If 2nd player chooses <PERSON><PERSON><PERSON><PERSON>, the 1st player continue playing the 2nd WHITE.
     * - No overlines.
     */
    SWAP,

    /**
     * - 1st player places 2 BLACKs and 1 WHITE anywhere.
     * - 2nd player chooses to be BLACK or WHITE:
     *   - If 2nd player chooses WHITE, they continue playing the 2nd WHITE.
     *   - If 2nd player chooses <PERSON><PERSON><PERSON><PERSON>, the 1st player continue playing the 2nd WHITE.
     *   - If they don't choose, they continue placing 1 <PERSON><PERSON><PERSON><PERSON> and 1 WHITE anywhere. Then the 1st player get to choose:
     *     - If 1st player chooses <PERSON>H<PERSON><PERSON>, they continue playing the 3rd WHITE.
     *     - If 1st player chooses BLACK, the 2nd player continue playing the 3rd WHITE.
     * - No overlines.
     */
    SWAP2,
}

export enum PIECE {
    BLACK,
    WHITE,
    EMPTY,
}

export enum DIRECTION {
    HORIZONTAL,
    VERTICAL,
    DIAGONAL,
    ANTI_DIAGONAL,
}

export enum BIT_BOARD {
    COLUMNS,
    ROWS,
}

export const PIECE_NAME = ["BLACK", "WHITE", "EMPTY"] as const;
export const PIECE_SYMBOL = ["X", "O", " "] as const;

export const DEFAULT_BOARD_SIZE = 15;
export const MIN_COLUMNS_OR_ROWS = 5;
export const MAX_COLUMNS_OR_ROWS = 26;
