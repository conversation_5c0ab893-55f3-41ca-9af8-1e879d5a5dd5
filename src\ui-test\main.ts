import { <PERSON>mo<PERSON> } from "@/games";

const game = new Gomoku();

const board = document.querySelector<HTMLPreElement>("#board");
const input = document.querySelector<HTMLInputElement>("#input");
const playBtn = document.querySelector<HTMLButtonElement>("#play");
const resetBtn = document.querySelector<HTMLButtonElement>("#reset");

const printBoard = () => {
    board!.innerText = game.ascii();
};

printBoard();

playBtn?.addEventListener("click", () => {
    const position = input?.value ?? "aa";
    game.move(position);
    printBoard();
});

resetBtn?.addEventListener("click", () => {
    game.resetBoard();
    printBoard();
});

const testBoard = [256, 32767, 253];
const open3 = testBoard[0] & testBoard[1] & testBoard[2];
console.log(open3);
