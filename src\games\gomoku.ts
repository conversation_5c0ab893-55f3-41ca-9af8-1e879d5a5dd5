import { Board, PIECE } from "@/core";

export class <PERSON><PERSON><PERSON> extends Board {
    protected parsePosition(position: string): { column: number; row: number } {
        if (position.length !== 2) {
            throw new Error("Invalid position");
        }

        const column = position[0].charCodeAt(0) - 97 + 1;
        const row = position[1].charCodeAt(0) - 97 + 1;

        if (column < 1 || column > this.columns || row < 1 || row > this.rows) {
            throw new Error("Invalid position");
        }

        return { column, row };
    }

    public move(position: string): void {
        const { column, row } = this.parsePosition(position);
        const boardIndex = this.getBoardIndex(column, row);
        if (this.piecesBoard[boardIndex] !== PIECE.EMPTY) {
            throw new Error(`Cannot set piece on occupied position: ${position}`);
        }

        // TODO: Check if move is valid according to game mode

        this.piecesBoard[boardIndex] = this.sideToMove;
        this.sideToMove ^= PIECE.WHITE;
    }
}
