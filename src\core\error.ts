export class CarojsError extends Error {
    constructor(message: string) {
        super(message); // Call the constructor of the base class `Error`
        this.name = "CarojsError"; // Set the error name to your custom error class name
        // Set the prototype explicitly to maintain the correct prototype chain
        Object.setPrototypeOf(this, CarojsError.prototype);
    }
}

export class CarojsErrorHandler {
    public messages: string[] = [];

    public check(condition: boolean, message: string): void {
        if (condition) {
            this.messages.push(message);
        }
    }

    public throw(): void {
        if (this.messages.length > 0) {
            throw new CarojsError(this.messages.join(" "));
        }
    }
}
