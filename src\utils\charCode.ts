import { isNumberInRange } from ".";

const LOWERCASE_A_CHAR_CODE = 97;
const MIN_INDEX = 0;
const MAX_INDEX = 25; // Maximum index for lowercase a-z

export const charToIndex = (char: string): number => {
    const index = char.charCodeAt(0) - LOWERCASE_A_CHAR_CODE;
    return isNumberInRange(index, MIN_INDEX, MAX_INDEX) ? index : -1;
};

export const indexToChar = (index: number): string => {
    return isNumberInRange(index, MIN_INDEX, MAX_INDEX) ? String.fromCharCode(LOWERCASE_A_CHAR_CODE + index) : "";
};
