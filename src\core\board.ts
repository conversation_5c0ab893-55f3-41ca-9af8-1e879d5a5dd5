// Enums
import { PIECE, DIRECTION, BIT_BOARD } from "@/core";

// Constants
import { PIECE_SYMBOL, DEFAULT_BOARD_SIZE, MIN_COLUMNS_OR_ROWS, MAX_COLUMNS_OR_ROWS } from "@/core";

// Utils
import { isNumberInRange, indexToChar, charToIndex } from "@/utils";

type BlackColumns = number[];
type WhiteColumns = number[];
type BlackRows = number[];
type WhiteRows = number[];
type ColumnsBoard = [BlackColumns, WhiteColumns];
type RowsBoard = [BlackRows, WhiteRows];

export class Board {
    public columns: number;
    public rows: number;

    protected piecesBoard: PIECE[];
    protected bitBoard: [ColumnsBoard, RowsBoard];

    protected sideToMove: PIECE.BLACK | PIECE.WHITE = PIECE.BLACK;
    protected moveCounts: number = 0;

    // TODO: History store + History traversal
    // TODO: Game state: win, draw, ongoing

    constructor(size?: number);
    constructor(columns: number, rows: number);
    constructor(columns?: number, rows?: number) {
        if (!columns || columns === undefined || !rows || rows === undefined) {
            columns = DEFAULT_BOARD_SIZE;
            rows = DEFAULT_BOARD_SIZE;
        }
        
        this.columns = this.isValidBoardSize(columns) ? columns : 0;
        this.rows = this.isValidBoardSize(rows) ? rows : 0;

        this.piecesBoard = new Array<PIECE>(this.columns * this.rows).fill(PIECE.EMPTY);

        const blackColumns: BlackColumns = new Array<number>(this.columns).fill(0);
        const whiteColumns: WhiteColumns = new Array<number>(this.columns).fill(0);
        const blackRows: BlackRows = new Array<number>(this.columns).fill(0);
        const whiteRows: WhiteRows = new Array<number>(this.columns).fill(0);
        const columnsBoard: ColumnsBoard = [blackColumns, whiteColumns];
        const rowsBoard: RowsBoard = [blackRows, whiteRows];
        this.bitBoard = [columnsBoard, rowsBoard];
    }

    protected isValidBoardSize(size: number): boolean {
        return Number.isInteger(size) && isNumberInRange(size, MIN_COLUMNS_OR_ROWS, MAX_COLUMNS_OR_ROWS);
    }

    protected isValidBoardSizeIndex(indexObject: { columnIndex: number } | { rowIndex: number }): boolean {
        const [index, value] = Object.entries(indexObject)[0];

        if (index !== "columnIndex" && index !== "rowIndex") return false;
        const sizeToCheck: "columns" | "rows" = index === "columnIndex" ? "columns" : "rows";

        return Number.isInteger(value) && isNumberInRange(value, 0, this[sizeToCheck] - 1);
    }

    protected isValidBoardIndex(boardIndex: number): boolean {
        return Number.isInteger(boardIndex) && isNumberInRange(boardIndex, 0, this.piecesBoard.length - 1);
    }

    protected isNthMove(nth: number): boolean {
        if (nth < 1) return false;
        return this.moveCounts === nth - 1;
    }

    protected isFirstMove(): boolean {
        return this.isNthMove(1);
    }

    public resetBoard() {
        this.piecesBoard.fill(PIECE.EMPTY);

        this.bitBoard[BIT_BOARD.COLUMNS][PIECE.BLACK].fill(0);
        this.bitBoard[BIT_BOARD.COLUMNS][PIECE.WHITE].fill(0);
        this.bitBoard[BIT_BOARD.ROWS][PIECE.BLACK].fill(0);
        this.bitBoard[BIT_BOARD.ROWS][PIECE.WHITE].fill(0);

        this.sideToMove = PIECE.BLACK;
        this.moveCounts = 0;
    }

    protected getBoardIndex(columnIndex: number, rowIndex: number): number {
        if (!this.isValidBoardSizeIndex({ columnIndex }) || !this.isValidBoardSizeIndex({ rowIndex })) return -1;

        return this.rows * rowIndex + columnIndex;
    }

    protected getBoardPiece(boardIndex: number): PIECE;
    protected getBoardPiece(columnIndex: number, rowIndex: number): PIECE;
    protected getBoardPiece(columnIndex: number, rowIndex?: number): PIECE {
        let boardIndex: number;

        if (!rowIndex || rowIndex === undefined) {
            boardIndex = columnIndex;
        } else {
            boardIndex = this.getBoardIndex(columnIndex, rowIndex);
        }

        if (!this.isValidBoardIndex(boardIndex)) return PIECE.EMPTY;
        return this.piecesBoard[boardIndex];
    }

    protected setBoardPiece(piece: PIECE.BLACK | PIECE.WHITE, boardIndex: number): boolean;
    protected setBoardPiece(piece: PIECE.BLACK | PIECE.WHITE, columnIndex: number, rowIndex: number): boolean;
    protected setBoardPiece(piece: PIECE.BLACK | PIECE.WHITE, columnIndex: number, rowIndex?: number): boolean {
        let boardIndex: number;

        if (!rowIndex || rowIndex === undefined) {
            boardIndex = columnIndex;
        } else {
            boardIndex = this.getBoardIndex(columnIndex, rowIndex);
        }

        if (!this.isValidBoardIndex(boardIndex)) return false;
        this.piecesBoard[boardIndex] = piece;

        return true;
    }

    protected getBitBoardMask(indexObject: { columnIndex: number } | { rowIndex: number }): number {
        const [index, value] = Object.entries(indexObject)[0];

        if (index !== "columnIndex" && index !== "rowIndex") return 0;
        const sizeToCheck: "columns" | "rows" = index === "columnIndex" ? "columns" : "rows";

        return 1 << (this[sizeToCheck] - value - 1);
    }

    protected setBitBoardPiece(piece: PIECE.BLACK | PIECE.WHITE, columnIndex: number, rowIndex: number): boolean {
        if (!this.isValidBoardSizeIndex({ columnIndex }) && !this.isValidBoardSizeIndex({ rowIndex })) return false;

        this.bitBoard[BIT_BOARD.COLUMNS][piece][columnIndex] |= this.getBitBoardMask({ rowIndex });
        this.bitBoard[BIT_BOARD.ROWS][piece][rowIndex] |= this.getBitBoardMask({ columnIndex });
        return true;
    }

    protected parsePosition(position: string): { columnIndex: number; rowIndex: number } {
        const error = { columnIndex: -1, rowIndex: -1 };
        if (position.length !== 2) return error;

        const columnIndex = charToIndex(position[0]);
        const rowIndex = charToIndex(position[1]);

        if (!this.isValidBoardSizeIndex({ columnIndex }) && !this.isValidBoardSizeIndex({ rowIndex })) {
            return error;
        }

        return { columnIndex, rowIndex };
    }

    protected placePiece(piece: PIECE.BLACK | PIECE.WHITE, columnIndex: number, rowIndex: number): number {
        const targetBoardIndex = this.getBoardIndex(columnIndex, rowIndex);
        if (targetBoardIndex === -1) return -1;

        const targetBoardPiece = this.getBoardPiece(targetBoardIndex);
        if (targetBoardPiece !== PIECE.EMPTY) return -2;

        const isSetBoardPieceSuccess = this.setBoardPiece(piece, targetBoardIndex);
        if (!isSetBoardPieceSuccess) return -3;

        const isSetBitBoardPieceSuccess = this.setBitBoardPiece(piece, columnIndex, rowIndex);
        if (!isSetBitBoardPieceSuccess) return -4;

        return 1;
    }

    public ascii(): string {
        let result = "";

        for (let rowIndex = -1; rowIndex < this.rows; rowIndex++) {
            for (let columnIndex = -1; columnIndex < this.columns; columnIndex++) {
                if (rowIndex < 0) {
                    result += columnIndex < 0 ? "   " : `[${indexToChar(columnIndex)}]`;
                } else {
                    const piece = this.getBoardPiece(columnIndex, rowIndex);
                    result += columnIndex < 0 ? `[${indexToChar(rowIndex)}]` : `[${PIECE_SYMBOL[piece]}]`;
                }
            }
            result += "\n";
        }

        return result;
    }
}
