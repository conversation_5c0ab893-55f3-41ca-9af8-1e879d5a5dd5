// Enums
import { PIECE, DIRECTION, BIT_BOARD } from "@/core";

// Constants
import { PIECE_NAME, PIECE_SYMBOL, DEFAULT_BOARD_SIZE, MIN_COLUMNS_OR_ROWS, MAX_COLUMNS_OR_ROWS } from "@/core";

// Utils
import { CarojsErrorHandler } from "@/core";
import { isNumberInRange, indexToChar, charToIndex } from "@/utils";

type BlackColumns = number[];
type WhiteColumns = number[];
type BlackRows = number[];
type WhiteRows = number[];
type ColumnsBoard = [BlackColumns, WhiteColumns];
type RowsBoard = [BlackRows, WhiteRows];

export class Board {
    protected columns: number;
    protected rows: number;

    protected piecesBoard!: PIECE[];
    protected bitBoard!: [ColumnsBoard, RowsBoard];

    protected sideToMove!: PIECE.BLACK | PIECE.WHITE;
    protected moveCounts!: number;

    // TODO: History store + History traversal
    // TODO: Game state: win, draw, ongoing

    constructor(size?: number);
    constructor(columns: number = DEFAULT_BOARD_SIZE, rows: number = DEFAULT_BOARD_SIZE) {
        const errorHandler = new CarojsErrorHandler();
        errorHandler.check(
            !this.isValidBoardSize(columns),
            `Invalid board columns: ${columns}. Columns must be between 5 and ${MAX_COLUMNS_OR_ROWS}.`
        );
        errorHandler.check(
            !this.isValidBoardSize(rows),
            `Invalid board rows: ${rows}. Rows must be between 5 and ${MAX_COLUMNS_OR_ROWS}.`
        );
        errorHandler.throw();

        this.columns = columns;
        this.rows = rows;
        this.resetBoard();
    }

    protected isValidBoardSize(size: number): boolean {
        return Number.isInteger(size) && isNumberInRange(size, MIN_COLUMNS_OR_ROWS, MAX_COLUMNS_OR_ROWS);
    }

    protected isValidBoardSizeIndex(indexObject: { columnIndex: number } | { rowIndex: number }): boolean {
        const [index, value] = Object.entries(indexObject)[0];

        if (index !== "columnIndex" && index !== "rowIndex") return false;
        const sizeToCheck: "columns" | "rows" = index === "columnIndex" ? "columns" : "rows";

        return Number.isInteger(value) && isNumberInRange(value, 0, this[sizeToCheck] - 1);
    }

    protected isValidBoardIndex(boardIndex: number): boolean {
        return Number.isInteger(boardIndex) && isNumberInRange(boardIndex, 0, this.piecesBoard.length - 1);
    }

    protected isNthMove(nth: number): boolean {
        if (nth < 1) return false;
        return this.moveCounts === nth - 1;
    }

    protected isFirstMove(): boolean {
        return this.isNthMove(1);
    }

    public resetBoard() {
        this.piecesBoard = new Array<PIECE>(this.columns * this.rows).fill(PIECE.EMPTY);

        const columnsBoard: ColumnsBoard = [[], []];
        columnsBoard[PIECE.BLACK] = new Array<number>(this.columns).fill(0);
        columnsBoard[PIECE.WHITE] = new Array<number>(this.columns).fill(0);
        const rowsBoard: RowsBoard = [[], []];
        rowsBoard[PIECE.BLACK] = new Array<number>(this.rows).fill(0);
        rowsBoard[PIECE.WHITE] = new Array<number>(this.rows).fill(0);
        this.bitBoard = [columnsBoard, rowsBoard];

        this.sideToMove = PIECE.BLACK;
        this.moveCounts = 0;
    }

    protected getBoardIndex(columnIndex: number, rowIndex: number): number {
        if (!this.isValidBoardSizeIndex({ columnIndex }) && !this.isValidBoardSizeIndex({ rowIndex })) return -1;

        return this.rows * rowIndex + columnIndex;
    }

    protected getBoardPiece(boardIndex: number): PIECE;
    protected getBoardPiece(columnIndex: number, rowIndex: number): PIECE;
    protected getBoardPiece(boardOrColumnIndex: number, rowIndex?: number): PIECE {
        if (!rowIndex) {
            if (!this.isValidBoardIndex(boardOrColumnIndex)) return PIECE.EMPTY;
            return this.piecesBoard[boardOrColumnIndex];
        }

        const boardIndex = this.getBoardIndex(boardOrColumnIndex, rowIndex);
        if (!this.isValidBoardIndex(boardIndex)) return PIECE.EMPTY;

        return this.piecesBoard[boardIndex];
    }

    protected setBoardPiece(piece: PIECE.BLACK | PIECE.WHITE, boardIndex: number): boolean;
    protected setBoardPiece(piece: PIECE.BLACK | PIECE.WHITE, columnIndex: number, rowIndex: number): boolean;
    protected setBoardPiece(piece: PIECE.BLACK | PIECE.WHITE, boardOrColumnIndex: number, rowIndex?: number): boolean {
        if (!rowIndex) {
            if (!this.isValidBoardIndex(boardOrColumnIndex)) return false;
            this.piecesBoard[boardOrColumnIndex] = piece;
            return true;
        }

        const boardIndex = this.getBoardIndex(boardOrColumnIndex, rowIndex);
        if (!this.isValidBoardIndex(boardIndex)) return false;

        this.piecesBoard[boardIndex] = piece;
        return true;
    }

    protected getBitBoardMask(indexObject: { columnIndex: number } | { rowIndex: number }): number {
        const [index, value] = Object.entries(indexObject)[0];

        if (index !== "columnIndex" && index !== "rowIndex") return 0;
        const sizeToCheck: "columns" | "rows" = index === "columnIndex" ? "columns" : "rows";

        return 1 << (this[sizeToCheck] - value - 1);
    }

    protected setBitBoardPiece(piece: PIECE.BLACK | PIECE.WHITE, columnIndex: number, rowIndex: number): boolean {
        if (!this.isValidBoardSizeIndex({ columnIndex }) && !this.isValidBoardSizeIndex({ rowIndex })) return false;

        this.bitBoard[BIT_BOARD.COLUMNS][piece][columnIndex] |= this.getBitBoardMask({ rowIndex });
        this.bitBoard[BIT_BOARD.ROWS][piece][rowIndex] |= this.getBitBoardMask({ columnIndex });
        return true;
    }

    protected parsePosition(position: string): { columnIndex: number; rowIndex: number } {
        const error = { columnIndex: -1, rowIndex: -1 };
        if (position.length !== 2) return error;

        const columnIndex = charToIndex(position[0]);
        const rowIndex = charToIndex(position[1]);

        if (!this.isValidBoardSizeIndex({ columnIndex }) && !this.isValidBoardSizeIndex({ rowIndex })) {
            return error;
        }

        return { columnIndex, rowIndex };
    }

    protected placePieceOnBoard(columnIndex: number, rowIndex: number, piece: PIECE.BLACK | PIECE.WHITE): void {
        const errorHandler = new CarojsErrorHandler();

        const targetBoardIndex = this.getBoardIndex(columnIndex, rowIndex);
        errorHandler.check(
            targetBoardIndex === -1,
            `Invalid position: ${indexToChar(columnIndex) + indexToChar(rowIndex)}.`
        );
        errorHandler.throw();

        const targetBoardPiece = this.getBoardPiece(targetBoardIndex);
        errorHandler.check(
            targetBoardPiece !== PIECE.EMPTY,
            `Occupied position: ${indexToChar(columnIndex) + indexToChar(rowIndex)} by ${PIECE_NAME[targetBoardPiece]}.`
        );
        errorHandler.throw();

        const isSetBoardPieceSuccess = this.setBoardPiece(piece, targetBoardIndex);
        errorHandler.check(
            !isSetBoardPieceSuccess,
            `Failed to set piece: ${PIECE_NAME[piece]} on position: ${
                indexToChar(columnIndex) + indexToChar(rowIndex)
            } to PiecesBoard.`
        );
        errorHandler.throw();

        const isSetBitBoardPieceSuccess = this.setBitBoardPiece(piece, columnIndex, rowIndex);
        errorHandler.check(
            !isSetBitBoardPieceSuccess,
            `Failed to set piece: ${PIECE_NAME[piece]} on position: ${
                indexToChar(columnIndex) + indexToChar(rowIndex)
            } to BitBoard.`
        );
        errorHandler.throw();
    }

    public ascii(): string {
        let result = "";

        for (let rowIndex = -1; rowIndex < this.rows; rowIndex++) {
            for (let columnIndex = -1; columnIndex < this.columns; columnIndex++) {
                if (rowIndex < 0) {
                    result += columnIndex < 0 ? "   " : `[${indexToChar(columnIndex)}]`;
                } else {
                    const piece = this.getBoardPiece(columnIndex, rowIndex);
                    result += columnIndex < 0 ? `[${indexToChar(rowIndex)}]` : `[${PIECE_SYMBOL[piece]}]`;
                }
            }
            result += "\n";
        }

        return result;
    }
}
